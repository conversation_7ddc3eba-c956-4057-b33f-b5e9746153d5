<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Factory\Flow\Loader;

use Doctrine\ORM\EntityManagerInterface;
use Flow\ETL\Loader;
use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\DatabaseBundle\Handler\EntityPersistRequestHandler;
use LoginAutonom\DatabaseBundle\Handler\TransactionHandler;
use LoginAutonom\EtlBundle\Entity\EtlExtractedLines;
use LoginAutonom\EtlBundle\Entity\EtlMapping;
use LoginAutonom\EtlBundle\Flow\Loader\EntitiesFromRowEntryLoader;
use LoginAutonom\EtlBundle\Interfaces\FlowLoaderFactoryInterface;

final readonly class EntitiesFromRowEntryLoaderFactory implements FlowLoaderFactoryInterface
{
    public const FIELD_NAME = 'fieldName';
    public const PERSIST_REASONS = 'persistReasons';
    public const LAST_ROW_IDENTIFIERS = 'lastRowIdentifiers';

    public function __construct(
        private EntityPersistRequestHandler $entityPersistRequestHandler,
        private CommandBusInterface $commandBus,
        private EntityManagerInterface $entityManager,
        private TransactionHandler $transactionHandler
    ) {
    }

    public function build(array $config, ?\DateTimeInterface $fromDate): Loader
    {
        $fieldName = $config[self::FIELD_NAME];
        $persistReasons = $config[self::PERSIST_REASONS];
        $persistReasons[EtlExtractedLines::class] = 'Save original ETL line';
        $persistReasons[EtlMapping::class] = 'Save ETL mapping';
        $lastRowIdentifiers = $config[self::LAST_ROW_IDENTIFIERS];

        return new EntitiesFromRowEntryLoader(
            $fieldName,
            $persistReasons,
            $lastRowIdentifiers,
            $this->entityPersistRequestHandler,
            $this->commandBus,
            $this->entityManager,
            $this->transactionHandler
        );
    }

    public static function getName(): string
    {
        return 'entities-from-row-entry';
    }
}
