<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Handler;

use LoginAutonom\CoreBundle\Storage\ContextStorage;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistRequest;
use LoginAutonom\DatabaseBundle\Storage\EntityRequestPersistStorage;
use LoginAutonom\DatabaseBundle\Storage\EntityToWriteStorage;

final class EntityPersistRequestHandler
{
    public function __construct(
        private ContextStorage $contextStorage,
        private EntityRequestPersistStorage $storage,
        private EntityToWriteStorage $entityToWriteStorage,
    ) {
    }

    public function handleMany(array $entities, string $reason): void
    {
        foreach ($entities as $entity) {
            $this->handle(
                new EntityPersistRequest($entity, $reason)
            );
        }
    }

    public function handle(EntityPersistRequest $request): void
    {
        if (!$request->hasUser() && $this->contextStorage->hasLoggedUser()) {
            $request->setUser($this->contextStorage->getLoggedUser());
        }
        $this->storage->add($request);
    }

    public function addEntityToWriteStorage(object $entity): void
    {
        $this->entityToWriteStorage->addEntity($entity);
    }

    public function addEntitiesToWriteStorage(array $entities): void
    {
        foreach ($entities as $entity) {
            $this->addEntityToWriteStorage($entity);
        }
    }
}
