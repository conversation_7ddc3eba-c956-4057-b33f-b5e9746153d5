<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

use LoginAutonom\CoreBundle\DTO\SingleValidity;
use LoginAutonom\CoreBundle\DTO\ValidityInterval;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Interfaces\ValidityAwareObjectInterface;
use LoginAutonom\CoreBundle\Interfaces\ValidityIntervalBasedObjectStorageInterface;

final class ValidityIntervalBasedEntity implements ValidityIntervalBasedObjectStorageInterface
{
    private array $entitiesByValidity = [];
    private mixed $identifier;
    private \Closure $getFromCallback;
    private \Closure $getToCallback;
    private \Closure $setFromCallback;
    private \Closure $setToCallback;

    public function __construct(
        mixed $identifier,
        \Closure $getFrom,
        \Closure $getTo,
        \Closure $setFrom,
        \Closure $setTo,
    ) {
        $this->identifier = $identifier;
        $this->getFromCallback = $getFrom;
        $this->getToCallback = $getTo;
        $this->setFromCallback = $setFrom;
        $this->setToCallback = $setTo;
    }

    public function add(object $object): void
    {
        $this->entitiesByValidity[spl_object_hash($object)] = $object;
    }

    public function get(ValidityAwareObjectInterface $validity): object|array
    {
        if ($validity instanceof SingleValidity) {
            return $this->getByDay($validity->getValidity());
        } elseif ($validity instanceof ValidityInterval) {
            return $this->getAllByDateInterval(
                $validity->getFrom(),
                $validity->getTo()
            );
        }
        throw new \InvalidArgumentException(
            'Invalid validity interval object type'
        );
    }

    public function getByDay(\DateTimeInterface $day): object
    {
        $fromCallback = $this->getFromCallback;
        $toCallback = $this->getToCallback;
        foreach ($this->entitiesByValidity as $entity) {
            $from = $fromCallback($entity);
            $to = $toCallback($entity);
            if (isset($to) && $from <= $day && $to >= $day) {
                return $entity;
            } elseif (!isset($to) && $from <= $day) {
                return $entity;
            }
        }

        $identifier = $this->identifier;
        if (is_array($identifier)) {
            $identifier = json_encode($this->identifier);
        }

        throw new NotFoundException(
            'entities with identifier: ' . $identifier,
            $day
        );
    }

    public function getAllByDateInterval(\DateTimeInterface $from, ?\DateTimeInterface $to): array
    {
        $collected = [];
        $fromCallback = $this->getFromCallback;
        $toCallback = $this->getToCallback;
        foreach ($this->entitiesByValidity as $entity) {
            $entityFrom = $fromCallback($entity);
            $entityTo = $toCallback($entity);
            if (isset($entityTo) && isset($to) && $entityFrom <= $to && $entityTo >= $from) {
                $collected[] = $entity;
            } elseif (isset($entityTo) && !isset($to) && $entityFrom <= $from && $from <= $entityTo) {
                $collected[] = $entity;
            } elseif (!isset($entityTo) && isset($to) && $entityFrom <= $from) {
                $collected[] = $entity;
            } elseif (!isset($entityTo) && !isset($to) && $entityFrom <= $from) {
                $collected[] = $entity;
            }
        }
        return $collected;
    }

    /**
     * @return object[]
     */
    public function getAll(): array
    {
        return $this->entitiesByValidity;
    }

    public function getFrom(object $object): ?\DateTimeInterface
    {
        return $this->getFromCallback->__invoke($object);
    }

    public function getTo(object $object): ?\DateTimeInterface
    {
        return $this->getToCallback->__invoke($object);
    }

    public function setFrom(object $object, ?\DateTimeInterface $day): void
    {
        $this->setFromCallback->__invoke($object, $day);
    }

    public function setTo(object $object, ?\DateTimeInterface $day): void
    {
        $this->setToCallback->__invoke($object, $day);
    }

    public function getIdentifier(): mixed
    {
        return $this->identifier;
    }

    public function hasIdentifier(): bool
    {
        return isset($this->identifier);
    }
}
