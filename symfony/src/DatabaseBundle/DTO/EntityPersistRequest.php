<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

use Symfony\Component\Security\Core\User\UserInterface;

final class EntityPersistRequest
{
    private UserInterface $user;
    private array $processIds;

    public function __construct(
        private object $entity,
        private string $reason,
        private bool $isMandatory = false,
        private ?EntityPersistContext $entityPersistContext = null,
    ) {
    }

    public function getEntity(): object
    {
        return $this->entity;
    }

    public function setEntity(object $entity): self
    {
        $this->entity = $entity;
        return $this;
    }

    public function hasEntity(): bool
    {
        return isset($this->entity);
    }

    public function getUser(): UserInterface
    {
        return $this->user;
    }

    public function setUser(UserInterface $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function hasUser(): bool
    {
        return isset($this->user);
    }

    public function getEntityPersistContext(): ?EntityPersistContext
    {
        return $this->entityPersistContext;
    }

    public function hasEntityPersistContext(): bool
    {
        return isset($this->entityPersistContext);
    }

    public function setEntityPersistContext(?EntityPersistContext $entityPersistContext): void
    {
        $this->entityPersistContext = $entityPersistContext;
    }

    public function getProcessIds(): array
    {
        return $this->processIds;
    }

    public function setProcessIds(array $processIds): self
    {
        $this->processIds = $processIds;
        return $this;
    }

    public function hasProcessIds(): bool
    {
        return isset($this->processIds);
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function setReason(string $reason): self
    {
        $this->reason = $reason;
        return $this;
    }

    public function hasReason(): bool
    {
        return isset($this->reason);
    }

    public function isMandatory(): bool
    {
        return $this->isMandatory;
    }

    public function setMandatory(): void
    {
        $this->isMandatory = true;
    }
}
