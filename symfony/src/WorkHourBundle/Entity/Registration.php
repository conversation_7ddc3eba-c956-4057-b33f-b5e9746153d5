<?php

declare(strict_types=1);

namespace LoginAutonom\WorkHourBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use LoginAutonom\DatabaseBundle\Attribute\DayColumn;
use LoginAutonom\DatabaseBundle\Attribute\EntityAssociation;
use LoginAutonom\DatabaseBundle\Attribute\StatusColumn;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\HistoryFieldsEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\NoteEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\PreRowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\RowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;
use LoginAutonom\EmployeeBundle\Entity\EmployeeCard;
use LoginAutonom\EmployeeBundle\Enum\EmployeeCardEntityFieldEnum;

#[ORM\Entity]
#[StatusColumn]
#[DayColumn(fieldName: 'time')]
#[ORM\Index(columns: [
        'card',
            'time',
            'event_type_id',
            'status',
        ], name: 'IDX_card_dt_event_status')]
#[ORM\Index(columns: [
    'card',
        'time',
        'status',
    ], name: 'IDX_card_dt_status')]
#[ORM\Index(columns: [
    'status',
        'pre_row_id',
    ], name: 'IDX_status_pre_row_id')]
#[ORM\Index(columns: [
    'sync',
    ], name: 'IDX_sync')]
#[ORM\Index(columns: [
    'time',
        'status',
    ], name: 'IDX_time_status')]
final class Registration
{
    #[ORM\Embedded(class: RowIdEmbeddable::class, columnPrefix: false)]
    private RowIdEmbeddable $row;

    #[ORM\Embedded(class: PreRowIdEmbeddable::class, columnPrefix: false)]
    private PreRowIdEmbeddable $preRow;

    #[ORM\Embedded(class: NoteEmbeddable::class, columnPrefix: false)]
    private NoteEmbeddable $note;

    #[ORM\Embedded(class: StatusEmbeddable::class, columnPrefix: false)]
    private StatusEmbeddable $status;

    #[ORM\Embedded(class: HistoryFieldsEmbeddable::class, columnPrefix: false)]
    private HistoryFieldsEmbeddable $history;

    #[ORM\Column(type: 'string', length: 32)]
    private string $terminalId;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    private ?string $readerId;

    #[EntityAssociation(
        targetEntity: EmployeeCard::class,
        targetField: EmployeeCardEntityFieldEnum::CARD
    )]
    #[ORM\Column(type: 'string', length: 32)]
    private string $card;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTime $time;

    #[ORM\Column(type: 'string', length: 32)]
    private string $eventTypeId;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    private ?string $costId;

    #[ORM\Column(type: 'bigint', nullable: true)]
    private ?int $sync;

    #[ORM\Column(type: 'tinyint', options: ['default' => '0'])]
    private int $calcStatus;

    #[ORM\Column(type: 'tinyint', nullable: true, options: ['default' => '2'])]
    private ?int $regStatus;

    #[ORM\Column(type: 'integer', options: ['default' => '0'])]
    private int $randomSelected;

    #[ORM\Column(type: 'string', length: 512, nullable: true)]
    private ?string $empNote;

    public function __construct()
    {
        $this->row = new RowIdEmbeddable();
        $this->note = new NoteEmbeddable();
        $this->status = new StatusEmbeddable();
        $this->history = new HistoryFieldsEmbeddable();
    }

    public function getRow(): RowIdEmbeddable
    {
        return $this->row;
    }

    public function setRow(RowIdEmbeddable $row): void
    {
        $this->row = $row;
    }

    public function hasRow(): bool
    {
        return isset($this->row);
    }

    public function getPreRow(): PreRowIdEmbeddable
    {
        return $this->preRow;
    }

    public function setPreRow(PreRowIdEmbeddable $preRow): void
    {
        $this->preRow = $preRow;
    }

    public function hasPreRow(): bool
    {
        return isset($this->preRow);
    }

    public function getNote(): NoteEmbeddable
    {
        return $this->note;
    }

    public function setNote(NoteEmbeddable $note): void
    {
        $this->note = $note;
    }

    public function hasNote(): bool
    {
        return isset($this->note);
    }

    public function getStatus(): StatusEmbeddable
    {
        return $this->status;
    }

    public function setStatus(StatusEmbeddable $status): void
    {
        $this->status = $status;
    }

    public function hasStatus(): bool
    {
        return isset($this->status);
    }

    public function getHistory(): HistoryFieldsEmbeddable
    {
        return $this->history;
    }

    public function setHistory(HistoryFieldsEmbeddable $history): void
    {
        $this->history = $history;
    }

    public function hasHistory(): bool
    {
        return isset($this->history);
    }

    public function getTerminalId(): string
    {
        return $this->terminalId;
    }

    public function setTerminalId(string $terminalId): void
    {
        $this->terminalId = $terminalId;
    }

    public function hasTerminalId(): bool
    {
        return isset($this->terminalId);
    }

    public function getReaderId(): ?string
    {
        return $this->readerId;
    }

    public function setReaderId(?string $readerId): void
    {
        $this->readerId = $readerId;
    }

    public function hasReaderId(): bool
    {
        return isset($this->readerId);
    }

    public function getCard(): string
    {
        return $this->card;
    }

    public function setCard(string $card): void
    {
        $this->card = $card;
    }

    public function hasCard(): bool
    {
        return isset($this->card);
    }

    public function getTime(): ?\DateTime
    {
        return $this->time;
    }

    public function setTime(?\DateTime $time): void
    {
        $this->time = $time;
    }

    public function hasTime(): bool
    {
        return isset($this->time);
    }

    public function getEventTypeId(): string
    {
        return $this->eventTypeId;
    }

    public function setEventTypeId(string $eventTypeId): void
    {
        $this->eventTypeId = $eventTypeId;
    }

    public function hasEventTypeId(): bool
    {
        return isset($this->eventTypeId);
    }

    public function getCostId(): ?string
    {
        return $this->costId;
    }

    public function setCostId(?string $costId): void
    {
        $this->costId = $costId;
    }

    public function hasCostId(): bool
    {
        return isset($this->costId);
    }

    public function getSync(): ?int
    {
        return $this->sync;
    }

    public function setSync(?int $sync): void
    {
        $this->sync = $sync;
    }

    public function hasSync(): bool
    {
        return isset($this->sync);
    }

    public function getCalcStatus(): int
    {
        return $this->calcStatus;
    }

    public function setCalcStatus(int $calcStatus): void
    {
        $this->calcStatus = $calcStatus;
    }

    public function hasCalcStatus(): bool
    {
        return isset($this->calcStatus);
    }

    public function getRegStatus(): ?int
    {
        return $this->regStatus;
    }

    public function setRegStatus(?int $regStatus): void
    {
        $this->regStatus = $regStatus;
    }

    public function hasRegStatus(): bool
    {
        return isset($this->regStatus);
    }

    public function getRandomSelected(): int
    {
        return $this->randomSelected;
    }

    public function setRandomSelected(int $randomSelected): void
    {
        $this->randomSelected = $randomSelected;
    }

    public function hasRandomSelected(): bool
    {
        return isset($this->randomSelected);
    }

    public function getEmpNote(): ?string
    {
        return $this->empNote;
    }

    public function setEmpNote(?string $empNote): void
    {
        $this->empNote = $empNote;
    }

    public function hasEmpNote(): bool
    {
        return isset($this->empNote);
    }
}
